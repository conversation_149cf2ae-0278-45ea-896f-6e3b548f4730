[tool:pytest]
testpaths = tests_pytest
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --disable-warnings
    -ra
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    network: marks tests that require network access
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
