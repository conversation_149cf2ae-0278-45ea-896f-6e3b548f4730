---
name: Bug report
about: Create a report to help us improve
title: '[BUG] '
labels: bug
assignees: ''

---

**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Expected behavior**
A clear and concise description of what you expected to happen.

**Patch content (if applicable)**
If the issue is related to a specific patch file, please provide:
- The patch content (or a minimal example that reproduces the issue)
- The target file content
- The command you used

**Environment (please complete the following information):**
- OS: [e.g. Ubuntu 20.04, Windows 10, macOS 12.0]
- Python version: [e.g. 3.9.7]
- python-patch version: [e.g. 1.16]
- Installation method: [e.g. pip, from source]

**Additional context**
Add any other context about the problem here.

**Error output**
If applicable, add the full error traceback or output.

```
Paste error output here
```
