[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "patch"
version = "1.0.0"
description = "Modern python patch utility to apply unified diffs"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "<PERSON> Qi<PERSON>", email = "<EMAIL>"}
]
maintainers = [
    {name = "<PERSON> Qi<PERSON>", email = "<EMAIL>"}
]
keywords = ["patch", "diff", "unified", "git", "mercurial", "svn"]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python",
    "Programming Language :: Python :: 2",
    "Programming Language :: Python :: 2.7",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.4",
    "Programming Language :: Python :: 3.5",
    "Programming Language :: Python :: 3.6",
    "Programming Language :: Python :: 3.7",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: System :: Archiving",
    "Topic :: Utilities"
]
requires-python = ">=3.10"

[project.urls]
Homepage = "https://github.com/techtonik/python-patch"
Repository = "https://github.com/techtonik/python-patch"
Issues = "https://github.com/techtonik/python-patch/issues"

[project.scripts]
patch = "patch.cli:main"

[tool.setuptools]
package-dir = {"" = "src"}

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
patch = ["py.typed"]

# Development dependencies
[project.optional-dependencies]
dev = [
    "pytest>=6.0",
    "pytest-cov",
    "black",
    "flake8",
    "mypy"
]

# Modern Python packaging configuration
[tool.black]
line-length = 88
target-version = ['py37']

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
