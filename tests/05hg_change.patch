diff -r 603f07175741 05hg_change.from
--- a/05hg_change.from	Wed Mar 16 08:21:44 2011 +0200
+++ b/05hg_change.to	Wed Mar 16 10:08:57 2011 +0200
@@ -262,6 +262,10 @@
         pto = patch.fromfile(join(tests_dir, "data/hg-added-file.diff"))
         self.assertEqual(pto.type, patch.HG)
 
+    def test_git_changed_detected(self):
+        pto = patch.fromfile(join(tests_dir, "data/git-changed-file.diff"))
+        self.assertEqual(pto.type, patch.GIT)
+
 class TestPatchApply(unittest.TestCase):
     def setUp(self):
         self.save_cwd = os.getcwdu()
