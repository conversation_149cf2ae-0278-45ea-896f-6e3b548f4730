diff --git a/jsonpickle/__init__.py b/jsonpickle/__init__.py
index 22161dd..ea5ca6d 100644
--- a/jsonpickle/__init__.py
+++ b/jsonpickle/__init__.py
@@ -87,7 +87,12 @@ class JSONPluginMgr(object):
         self._decoders = {}
 
         ## Options to pass to specific encoders
-        self._encoder_options = {}
+        json_opts = ((), {'sort_keys': True})
+        self._encoder_options = {
+            'json': json_opts,
+            'simplejson': json_opts,
+            'django.util.simplejson': json_opts,
+        }
 
         ## The exception class that is thrown when a decoding error occurs
         self._decoder_exceptions = {}
diff --git a/tests/jsonpickle_test.py b/tests/jsonpickle_test.py
index c61dec4..09ba339 100644
--- a/tests/jsonpickle_test.py
+++ b/tests/jsonpickle_test.py
@@ -427,6 +427,15 @@ class PicklingTestCase(unittest.TestCase):
         inflated = self.unpickler.restore(flattened)
         self.assertEqual(obj, inflated)
 
+    def test_references(self):
+        obj_a = Thing('foo')
+        obj_b = Thing('bar')
+        coll = [obj_a, obj_b, obj_b]
+        flattened = self.pickler.flatten(coll)
+        inflated = self.unpickler.restore(flattened)
+        self.assertEqual(len(inflated), len(coll))
+        for x in range(len(coll)):
+            self.assertEqual(repr(coll[x]), repr(inflated[x]))
 
 class JSONPickleTestCase(unittest.TestCase):
     def setUp(self):
