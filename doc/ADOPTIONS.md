| Project | Description | patch.py version | Reviewed |
|:--------|:------------|:-----------------|:---------|
| [conda-recipes](https://github.com/conda/conda-recipes/tree/master/python-patch)| conda package | [1.12.11](https://github.com/conda/conda-recipes/blob/master/python-patch/patch.py) | 2016-01-17 | 
| [collective.recipe.patch](https://pypi.python.org/pypi/collective.recipe.patch/0.2.2) | buildout recipe for patching eggs | [8.06-1+](https://github.com/garbas/collective.recipe.patch/blob/master/collective/recipe/patch/patch.py) | 2014-01-17 |
| [Linux Kernel Backports](https://backports.wiki.kernel.org/index.php/Documentation) | backporting Linux upstream device drivers for usage on older kernels | [1.12.12dev+](https://git.kernel.org/cgit/linux/kernel/git/backports/backports.git/tree/lib/patch.py) | 2014-01-17 |
| [LuaPatch](http://lua-users.org/wiki/LuaPatch) | rewrite of patch.py for Lua by David Manura | 8.06-1| 2014-01-17 |
| [OpenHatch](https://openhatch.org/) | help wannabe open source developers find interesting projects | [10.04-2+](https://github.com/openhatch/oh-mainline/blob/master/vendor/packages/python-patch/patch.py) | 2014-01-17 |
| [nose](https://nose.readthedocs.org/en/latest/) | `nose` extends unittest to make testing easier | [10.04-2+](https://github.com/nose-devs/nose/blob/master/patch.py) | 2014-01-17 |
| [pypatch](https://pypi.python.org/pypi/pypatch/0.5.1) | automatically patch installed python modules | 1.12.11 | 2014-01-17 |
