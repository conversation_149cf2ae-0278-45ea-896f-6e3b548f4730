<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->

<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   version="1.0"
   width="744.09448"
   height="1052.3622"
   id="svg2"
   sodipodi:version="0.32"
   inkscape:version="0.48.4 r9939"
   sodipodi:docname="unified_diff_svn.svg"
   inkscape:output_extension="org.inkscape.output.svg.inkscape"
   inkscape:export-filename="E:\p\_py\rietveld\.rietveld\rietveld2\python-patch-read-only\doc\unified_diff_svn.png"
   inkscape:export-xdpi="90"
   inkscape:export-ydpi="90">
  <metadata
     id="metadata321">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title></dc:title>
      </cc:Work>
    </rdf:RDF>
  </metadata>
  <sodipodi:namedview
     inkscape:window-height="800"
     inkscape:window-width="1280"
     inkscape:pageshadow="2"
     inkscape:pageopacity="0.0"
     guidetolerance="10.0"
     gridtolerance="10.0"
     objecttolerance="10.0"
     borderopacity="1.0"
     bordercolor="#666666"
     pagecolor="#ffffff"
     id="base"
     showgrid="false"
     inkscape:zoom="1"
     inkscape:cx="326.14062"
     inkscape:cy="619.06721"
     inkscape:window-x="-4"
     inkscape:window-y="-4"
     inkscape:current-layer="svg2"
     inkscape:window-maximized="0" />
  <defs
     id="defs4">
    <inkscape:perspective
       sodipodi:type="inkscape:persp3d"
       inkscape:vp_x="0 : 526.18109 : 1"
       inkscape:vp_y="0 : 1000 : 0"
       inkscape:vp_z="744.09448 : 526.18109 : 1"
       inkscape:persp3d-origin="372.04724 : 350.78739 : 1"
       id="perspective323" />
  </defs>
  <rect
     style="fill:#d3d3d3;fill-opacity:1;stroke:#000000;stroke-width:0.5;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
     id="rect3986"
     y="119.08554"
     x="29.101612"
     height="24.043991"
     width="542.27417" />
  <rect
     style="fill:#d3d3d3;fill-opacity:1;stroke:#000000;stroke-width:0.5;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
     id="rect4079"
     y="643.3783"
     x="29.101664"
     height="21.006182"
     width="542.31421" />
  <rect
     style="fill:#faffc9;fill-opacity:1;stroke:#000000;stroke-width:0.49999997;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
     id="rect4077"
     y="530.40411"
     x="29.101612"
     height="112.92788"
     width="542.34729" />
  <rect
     style="fill:#faffc9;fill-opacity:1;stroke:#000000;stroke-width:0.49999997;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
     id="rect4075"
     y="313.9101"
     x="29.101612"
     height="217.22546"
     width="542.17633" />
  <rect
     style="fill:#faffc9;fill-opacity:1;stroke:#000000;stroke-width:0.5;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
     id="rect4073"
     y="162.67993"
     x="29.101612"
     height="154.54366"
     width="542.27075" />
  <rect
     style="fill:#c9ffd5;fill-opacity:1;stroke:#000000;stroke-width:0.5;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
     id="rect4071"
     y="141.73454"
     x="29.101612"
     height="22.392969"
     width="542.25354" />
  <rect
     style="fill:#faffc9;fill-opacity:1;stroke:#000000;stroke-width:0.5;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
     id="rect4419"
     y="683.94727"
     x="29.101612"
     height="299.80609"
     width="542.34729" />
  <rect
     style="fill:#c9ffd5;fill-opacity:1;stroke:#000000;stroke-width:0.5;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
     id="rect4421"
     y="663.87445"
     x="29.101664"
     height="21.006186"
     width="542.31421" />
  <path
     style="fill:#ffcfc9;fill-opacity:1;stroke:#000000;stroke-width:0.5;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
     id="rect4536"
     d="M 29.242421,164.12501 L 29.242421,174.27697 L 157.24347,174.27697 L 157.24347,164.12501 L 29.242421,164.12501 z" />
  <path
     style="fill:#ffcfc9;fill-opacity:1;stroke:#000000;stroke-width:0.5;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
     id="path4559"
     d="M 29.242421,317.36739 L 29.242421,327.51935 L 157.24347,327.51935 L 157.24347,317.36739 L 29.242421,317.36739 z" />
  <path
     style="fill:#ffcfc9;fill-opacity:1;stroke:#000000;stroke-width:0.5;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
     id="path4561"
     d="M 29.242421,531.36739 L 29.242421,541.51935 L 157.24347,541.51935 L 157.24347,531.36739 L 29.242421,531.36739 z" />
  <path
     style="fill:#ffcfc9;fill-opacity:1;stroke:#000000;stroke-width:0.5;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
     id="path4563"
     d="M 29.242421,684.86231 L 29.242421,695.01427 L 157.24347,695.01427 L 157.24347,684.86231 L 29.242421,684.86231 z" />
  <text
     style="font-size:28px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:125%;writing-mode:lr-tb;text-anchor:start;fill:#000000;fill-opacity:1;stroke:none;font-family:Sans;-inkscape-font-specification:Sans"
     xml:space="preserve"
     id="text3113"
     y="55.509674"
     x="25.596079"
     transform="scale(1.0216856,0.97877469)"
     sodipodi:linespacing="125%"><tspan
       y="55.509674"
       x="25.596079"
       sodipodi:role="line"
       id="tspan4421">Unified Diff/Patch Format (Subversion)</tspan></text>
  <g
     id="g4529"
     transform="matrix(1.0472513,0,0,1.0032667,-0.2576625,-0.3375146)">
    <rect
       style="fill:#d3d3d3;fill-opacity:1;stroke:#000000;stroke-width:0.97558779;stroke-opacity:1"
       id="rect3125"
       y="75.54467"
       x="28.284271"
       height="27.274118"
       width="122.22845" />
    <text
       sodipodi:linespacing="125%"
       style="font-size:40px;font-style:normal;font-weight:normal;line-height:125%;fill:#000000;fill-opacity:1;stroke:none;stroke-width:1px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1;font-family:Bitstream Vera Sans"
       xml:space="preserve"
       id="text3897"
       y="91.36142"
       x="41.263729"><tspan
         style="font-size:8px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:125%;writing-mode:lr-tb;text-anchor:start;font-family:DejaVu Sans Mono;-inkscape-font-specification:DejaVu Sans Mono"
         id="tspan3899"
         y="91.36142"
         x="41.263729">comments are ignored</tspan></text>
  </g>
  <g
     id="g4520"
     transform="matrix(1.0472513,0,0,1.0032667,-0.3960063,-0.3375146)">
    <rect
       style="fill:#c9ffd5;fill-opacity:1;stroke:#000000;stroke-width:0.97558779;stroke-opacity:1"
       id="rect3908"
       y="75.54467"
       x="160.10918"
       height="27.274118"
       width="122.22845" />
    <text
       sodipodi:linespacing="125%"
       style="font-size:8px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:center;line-height:125%;writing-mode:lr-tb;text-anchor:middle;fill:#000000;fill-opacity:1;stroke:none;stroke-width:1px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1;font-family:DejaVu Sans Mono;-inkscape-font-specification:DejaVu Sans Mono"
       xml:space="preserve"
       id="text3910"
       y="87.320808"
       x="220.56581"><tspan
         style="font-size:8px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:center;line-height:125%;writing-mode:lr-tb;text-anchor:middle;font-family:DejaVu Sans Mono;-inkscape-font-specification:DejaVu Sans Mono"
         id="tspan3912"
         y="87.320808"
         x="220.56581">the first file that</tspan><tspan
         style="font-size:8px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:center;line-height:125%;writing-mode:lr-tb;text-anchor:middle;font-family:DejaVu Sans Mono;-inkscape-font-specification:DejaVu Sans Mono"
         id="tspan3914"
         y="97.320808"
         x="220.56581">exists is used</tspan></text>
  </g>
  <g
     id="g4504"
     transform="matrix(1.0472513,0,0,1.0032667,-0.5343536,-0.3375146)">
    <rect
       style="fill:#faffc9;fill-opacity:1;stroke:#000000;stroke-width:0.97558779;stroke-opacity:1"
       id="rect3926"
       y="75.54467"
       x="291.93408"
       height="27.274118"
       width="122.22845" />
    <text
       sodipodi:linespacing="125%"
       style="font-size:8px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:center;line-height:125%;writing-mode:lr-tb;text-anchor:middle;fill:#000000;fill-opacity:1;stroke:none;stroke-width:1px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1;font-family:DejaVu Sans Mono;-inkscape-font-specification:DejaVu Sans Mono"
       xml:space="preserve"
       id="text3928"
       y="87.320808"
       x="352.39072"><tspan
         style="font-size:8px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:center;line-height:125%;writing-mode:lr-tb;text-anchor:middle;font-family:DejaVu Sans Mono;-inkscape-font-specification:DejaVu Sans Mono"
         id="tspan3960"
         y="87.320808"
         x="352.39072">may contain several</tspan><tspan
         style="font-size:8px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:center;line-height:125%;writing-mode:lr-tb;text-anchor:middle;font-family:DejaVu Sans Mono;-inkscape-font-specification:DejaVu Sans Mono"
         id="tspan3964"
         y="97.320808"
         x="352.39072">hunks for each file</tspan><tspan
         style="font-size:8px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:center;line-height:125%;writing-mode:lr-tb;text-anchor:middle;font-family:DejaVu Sans Mono;-inkscape-font-specification:DejaVu Sans Mono"
         id="tspan3958"
         y="107.32081"
         x="352.39072" /></text>
  </g>
  <g
     id="g4486"
     transform="matrix(1.0472513,0,0,1.0032667,-0.2576418,-0.3375146)">
    <rect
       style="fill:#ffcfc9;fill-opacity:1;stroke:#000000;stroke-width:0.97558779;stroke-opacity:1"
       id="rect3970"
       y="75.54467"
       x="423.36264"
       height="27.274118"
       width="122.22845" />
    <text
       sodipodi:linespacing="125%"
       style="font-size:8px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:center;line-height:125%;writing-mode:lr-tb;text-anchor:middle;fill:#000000;fill-opacity:1;stroke:none;stroke-width:1px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1;font-family:DejaVu Sans Mono;-inkscape-font-specification:DejaVu Sans Mono"
       xml:space="preserve"
       id="text3972"
       y="87.320808"
       x="483.81927"><tspan
         style="font-size:8px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:center;line-height:125%;writing-mode:lr-tb;text-anchor:middle;fill:#000000;fill-opacity:1;font-family:DejaVu Sans Mono;-inkscape-font-specification:DejaVu Sans Mono"
         id="tspan3976"
         y="87.320808"
         x="483.81927">-line_from,total_before</tspan><tspan
         style="font-size:8px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:center;line-height:125%;writing-mode:lr-tb;text-anchor:middle;fill:#000000;fill-opacity:1;font-family:DejaVu Sans Mono;-inkscape-font-specification:DejaVu Sans Mono"
         id="tspan3978"
         y="97.320808"
         x="483.81927">+line_after,total_after</tspan></text>
  </g>
  <g
     id="g4567"
     transform="matrix(1.0472513,0,0,1.0032667,423.10537,67.948198)">
    <rect
       style="fill:#c9ffd5;fill-opacity:1;stroke:#000000;stroke-width:0.97558779;stroke-opacity:1"
       id="rect4569"
       y="75.54467"
       x="160.10918"
       height="27.274118"
       width="122.22845" />
    <text
       sodipodi:linespacing="125%"
       style="font-size:8px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:center;line-height:125%;writing-mode:lr-tb;text-anchor:middle;fill:#000000;fill-opacity:1;stroke:none;stroke-width:1px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1;font-family:DejaVu Sans Mono;-inkscape-font-specification:DejaVu Sans Mono"
       xml:space="preserve"
       id="text4571"
       y="87.320808"
       x="220.56581"><tspan
         style="font-size:8px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:center;line-height:125%;writing-mode:lr-tb;text-anchor:middle;font-family:DejaVu Sans Mono;-inkscape-font-specification:DejaVu Sans Mono"
         id="tspan4583"
         y="87.320808"
         x="220.56581">--- filename \t comment</tspan></text>
  </g>
  <g
     id="g4587"
     transform="matrix(1.0472513,0,0,1.0032667,283.99712,828.76607)">
    <rect
       style="fill:#faffc9;fill-opacity:1;stroke:#000000;stroke-width:0.97558779;stroke-opacity:1"
       id="rect4589"
       y="75.54467"
       x="291.93408"
       height="27.274118"
       width="122.22845" />
    <text
       sodipodi:linespacing="125%"
       style="font-size:8px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:center;line-height:125%;writing-mode:lr-tb;text-anchor:middle;fill:#000000;fill-opacity:1;stroke:none;stroke-width:1px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1;font-family:DejaVu Sans Mono;-inkscape-font-specification:DejaVu Sans Mono"
       xml:space="preserve"
       id="text4591"
       y="91.348259"
       x="352.39072"><tspan
         style="font-size:8px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:center;line-height:125%;writing-mode:lr-tb;text-anchor:middle;font-family:DejaVu Sans Mono;-inkscape-font-specification:DejaVu Sans Mono"
         id="tspan4597"
         y="91.348259"
         x="352.39072">line ends may differ</tspan></text>
  </g>
  <g
     id="g3056"
     transform="translate(6.1391031e-6,-2.2888184e-5)">
    <rect
       style="fill:#ffcfc9;fill-opacity:1;stroke:#000000;stroke-width:1;stroke-opacity:1"
       id="rect4606"
       y="180.776"
       x="590.77991"
       height="41.044823"
       width="128.00391" />
    <text
       transform="scale(1.0216856,0.9787747)"
       sodipodi:linespacing="125%"
       style="font-size:8.20018482px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:center;line-height:125%;writing-mode:lr-tb;text-anchor:middle;fill:#000000;fill-opacity:1;stroke:none;stroke-width:1px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1;font-family:DejaVu Sans Mono;-inkscape-font-specification:DejaVu Sans Mono"
       xml:space="preserve"
       id="text4608"
       y="197.62608"
       x="640.2099"><tspan
         style="font-size:8.20018482px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:center;line-height:125%;writing-mode:lr-tb;text-anchor:middle;fill:#000000;fill-opacity:1;font-family:DejaVu Sans Mono;-inkscape-font-specification:DejaVu Sans Mono"
         id="tspan4622"
         y="197.62608"
         x="640.2099">for the format like</tspan><tspan
         style="font-size:8.20018482px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:center;line-height:125%;writing-mode:lr-tb;text-anchor:middle;fill:#000000;fill-opacity:1;font-family:DejaVu Sans Mono;-inkscape-font-specification:DejaVu Sans Mono"
         id="tspan4626"
         y="207.87631"
         x="640.2099">-line_from +line_after</tspan><tspan
         style="font-size:8.20018482px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:center;line-height:125%;writing-mode:lr-tb;text-anchor:middle;fill:#000000;fill-opacity:1;font-family:DejaVu Sans Mono;-inkscape-font-specification:DejaVu Sans Mono"
         id="tspan4616"
         y="218.12654"
         x="640.2099">total_xxx = 1</tspan></text>
  </g>
  <g
     id="g4640"
     transform="matrix(1.0472513,0,0,1.0032667,284.28523,864.45712)">
    <g
       id="g4670"
       transform="translate(-0.2751091,3.9869783)">
      <rect
         style="fill:#faffc9;fill-opacity:1;stroke:#000000;stroke-width:0.97558779;stroke-opacity:1"
         id="rect4642"
         y="73.76046"
         x="291.93408"
         height="40.911179"
         width="122.22845" />
      <text
         sodipodi:linespacing="125%"
         style="font-size:8px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:center;line-height:125%;writing-mode:lr-tb;text-anchor:middle;fill:#000000;fill-opacity:1;stroke:none;stroke-width:1px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1;font-family:DejaVu Sans Mono;-inkscape-font-specification:DejaVu Sans Mono"
         xml:space="preserve"
         id="text4644"
         y="87.320808"
         x="352.39072"><tspan
           style="font-size:8px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:center;line-height:125%;writing-mode:lr-tb;text-anchor:middle;font-family:DejaVu Sans Mono;-inkscape-font-specification:DejaVu Sans Mono"
           id="tspan4668"
           y="87.320808"
           x="354.79697">&quot;\ No newline at end of </tspan><tspan
           style="font-size:8px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:center;line-height:125%;writing-mode:lr-tb;text-anchor:middle;font-family:DejaVu Sans Mono;-inkscape-font-specification:DejaVu Sans Mono"
           id="tspan5786"
           y="97.320808"
           x="352.39072">file&quot; marker is used if</tspan><tspan
           style="font-size:8px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:center;line-height:125%;writing-mode:lr-tb;text-anchor:middle;font-family:DejaVu Sans Mono;-inkscape-font-specification:DejaVu Sans Mono"
           id="tspan5800"
           y="107.32081"
           x="352.39072">file ends without newline</tspan><tspan
           style="font-size:8px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:center;line-height:125%;writing-mode:lr-tb;text-anchor:middle;font-family:DejaVu Sans Mono;-inkscape-font-specification:DejaVu Sans Mono"
           id="tspan5784"
           y="117.32081"
           x="352.39072" /></text>
    </g>
  </g>
  <text
     sodipodi:linespacing="125%"
     style="font-size:8.1734848px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:125%;writing-mode:lr-tb;text-anchor:start;fill:#000000;fill-opacity:1;stroke:none;stroke-width:1px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1;font-family:Bitstream Vera Sans;-inkscape-font-specification:Bitstream Vera Sans"
     xml:space="preserve"
     id="text5802"
     y="1030.8047"
     x="37.980774"
     transform="scale(1.0216856,0.9787747)"><tspan
       id="tspan5804"
       y="1030.8047"
       x="37.980774">http://en.wikipedia.org/wiki/Diff#Unified_format</tspan></text>
  <text
     sodipodi:linespacing="125%"
     style="font-size:8.1734848px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:125%;writing-mode:lr-tb;text-anchor:start;fill:#000000;fill-opacity:1;stroke:none;stroke-width:1px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1;font-family:Bitstream Vera Sans;-inkscape-font-specification:Bitstream Vera Sans"
     xml:space="preserve"
     id="text5806"
     y="1041.0216"
     x="37.980774"
     transform="scale(1.0216856,0.9787747)"><tspan
       id="tspan5808"
       y="1041.0216"
       x="37.980774">http://techtonik.rainforce.org</tspan></text>
  <text
     sodipodi:linespacing="120%"
     style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;fill:#000000;fill-opacity:1;stroke:none;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
     xml:space="preserve"
     id="text4629"
     transform="scale(0.96076502,1.0408372)"
     y="126.03973"
     x="39.688015"><tspan
       id="tspan4631"
       y="126.03973"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4633"
         y="126.03973"
         x="39.688015">Index: src/plugins/contrib/devpak_plugin/updatedlg.cpp</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4635"
         dx="0"
         y="126.03973"
         x="305.19858" /></tspan><tspan
       id="tspan4637"
       y="135.84792"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4639"
         y="135.84792"
         x="39.688015">===================================================================</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4641"
         dx="0"
         y="135.84792"
         x="369.11774" /></tspan><tspan
       id="tspan4643"
       y="145.65608"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4645"
         y="145.65608"
         x="39.688015">--- src/plugins/contrib/devpak_plugin/updatedlg.cpp (revision 5106)</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4647"
         dx="0"
         y="145.65608"
         x="369.11774" /></tspan><tspan
       id="tspan4649"
       y="155.46426"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4651"
         y="155.46426"
         x="39.688015">+++ src/plugins/contrib/devpak_plugin/updatedlg.cpp (working copy)</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4653"
         dx="0"
         y="155.46426"
         x="364.20087" /></tspan><tspan
       id="tspan4655"
       y="165.27246"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4657"
         y="165.27246"
         x="39.688015">@@ -94,11 +94,13 @@</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4659"
         dx="0"
         y="165.27246"
         x="133.10837" /></tspan><tspan
       id="tspan4661"
       y="175.08063"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4663"
         y="175.08063"
         x="39.688015">     lst-&gt;InsertColumn(1, _(&quot;Version&quot;));</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4665"
         dx="0"
         y="175.08063"
         x="236.36249" /></tspan><tspan
       id="tspan4667"
       y="184.88881"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4669"
         y="184.88881"
         x="39.688015">     lst-&gt;InsertColumn(2, _(&quot;Installed&quot;));</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4671"
         dx="0"
         y="184.88881"
         x="246.19618" /></tspan><tspan
       id="tspan4673"
       y="194.69701"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4675"
         y="194.69701"
         x="39.688015">     lst-&gt;InsertColumn(3, _(&quot;Size&quot;), wxLIST_FORMAT_RIGHT);</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4677"
         dx="0"
         y="194.69701"
         x="324.866" /></tspan><tspan
       id="tspan4679"
       y="204.5052"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4681"
         y="204.5052"
         x="39.688015">+    lst-&gt;InsertColumn(4, _(&quot;Rev&quot;));</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4683"
         dx="0"
         y="204.5052"
         x="216.69504" /></tspan><tspan
       id="tspan4685"
       y="214.31337"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4687"
         y="214.31337"
         x="39.688015"> </tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4689"
         dx="0"
         y="214.31337"
         x="44.604874" /></tspan><tspan
       id="tspan4691"
       y="224.12157"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4693"
         y="224.12157"
         x="39.688015">-    lst-&gt;SetColumnWidth(0, lst-&gt;GetSize().x - (64 * 3) - 2); // 1st column takes all remaining space</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4695"
         dx="0"
         y="224.12157"
         x="536.29102" /></tspan><tspan
       id="tspan4697"
       y="233.92973"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4699"
         y="233.92973"
         x="39.688015">+    lst-&gt;SetColumnWidth(0, lst-&gt;GetSize().x - (64 * 3 + 40) - 6 ); // 1st column takes all remaining space</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4701"
         dx="0"
         y="233.92973"
         x="565.79218" /></tspan><tspan
       id="tspan4703"
       y="243.73793"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4705"
         y="243.73793"
         x="39.688015">     lst-&gt;SetColumnWidth(1, 64);</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4707"
         dx="0"
         y="243.73793"
         x="197.02759" /></tspan><tspan
       id="tspan4709"
       y="253.54611"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4711"
         y="253.54611"
         x="39.688015">     lst-&gt;SetColumnWidth(2, 64);</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4713"
         dx="0"
         y="253.54611"
         x="197.02759" /></tspan><tspan
       id="tspan4715"
       y="263.35431"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4717"
         y="263.35431"
         x="39.688015">     lst-&gt;SetColumnWidth(3, 64);</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4719"
         dx="0"
         y="263.35431"
         x="197.02759" /></tspan><tspan
       id="tspan4721"
       y="273.16251"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4723"
         y="273.16251"
         x="39.688015">+    lst-&gt;SetColumnWidth(4, 40);</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4725"
         dx="0"
         y="273.16251"
         x="197.02759" /></tspan><tspan
       id="tspan4727"
       y="282.97067"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4729"
         y="282.97067"
         x="39.688015"> }</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4731"
         dx="0"
         y="282.97067"
         x="49.521736" /></tspan><tspan
       id="tspan4733"
       y="292.77884"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4735"
         y="292.77884"
         x="39.688015"> </tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4737"
         dx="0"
         y="292.77884"
         x="44.604874" /></tspan><tspan
       id="tspan4739"
       y="302.58707"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4741"
         y="302.58707"
         x="39.688015"> void UpdateDlg::AddRecordToList(UpdateRec* rec)</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4743"
         dx="0"
         y="302.58707"
         x="275.69739" /></tspan><tspan
       id="tspan4745"
       y="312.3952"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4747"
         y="312.3952"
         x="39.688015">@@ -111,8 +113,20 @@</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4749"
         dx="0"
         y="312.3952"
         x="138.02525" /></tspan><tspan
       id="tspan4751"
       y="322.2034"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4753"
         y="322.2034"
         x="39.688015">     lst-&gt;SetItem(idx, 1, rec-&gt;version);</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4755"
         dx="0"
         y="322.2034"
         x="236.36249" /></tspan><tspan
       id="tspan4757"
       y="332.01157"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4759"
         y="332.01157"
         x="39.688015">     lst-&gt;SetItem(idx, 2, rec-&gt;installed_version);</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4761"
         dx="0"
         y="332.01157"
         x="285.5311" /></tspan><tspan
       id="tspan4763"
       y="341.81973"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4765"
         y="341.81973"
         x="39.688015">     lst-&gt;SetItem(idx, 3, rec-&gt;size);</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4767"
         dx="0"
         y="341.81973"
         x="221.61188" /></tspan><tspan
       id="tspan4769"
       y="351.6279"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4771"
         y="351.6279"
         x="39.688015">+    lst-&gt;SetItem(idx, 4, rec-&gt;revision);</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4773"
         dx="0"
         y="351.6279"
         x="241.27933" /></tspan><tspan
       id="tspan4775"
       y="361.4361"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4777"
         y="361.4361"
         x="39.688015"> }</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4779"
         dx="0"
         y="361.4361"
         x="49.521736" /></tspan><tspan
       id="tspan4781"
       y="371.24423"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4783"
         y="371.24423"
         x="39.688015"> </tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4785"
         dx="0"
         y="371.24423"
         x="44.604874" /></tspan><tspan
       id="tspan4787"
       y="381.05243"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4789"
         y="381.05243"
         x="39.688015">+wxString UpdateDlg::GetListColumnText(int idx, int col) {</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4791"
         dx="0"
         y="381.05243"
         x="324.866" /></tspan><tspan
       id="tspan4793"
       y="390.8606"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4795"
         y="390.8606"
         x="39.688015">+    wxListCtrl* lst = XRCCTRL(*this, &quot;lvFiles&quot;, wxListCtrl);</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4797"
         dx="0"
         y="390.8606"
         x="339.61658" /></tspan><tspan
       id="tspan4799"
       y="400.66876"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4801"
         y="400.66876"
         x="39.688015">+    int index = idx == -1 ? lst-&gt;GetNextItem(-1, wxLIST_NEXT_ALL, wxLIST_STATE_SELECTED) : idx;</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4803"
         dx="0"
         y="400.66876"
         x="511.70673" /></tspan><tspan
       id="tspan4805"
       y="410.47693"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4807"
         y="410.47693"
         x="39.688015">+    wxListItem info;</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4809"
         dx="0"
         y="410.47693"
         x="142.94209" /></tspan><tspan
       id="tspan4811"
       y="420.28513"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4813"
         y="420.28513"
         x="39.688015">+    info.SetId(index);</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4815"
         dx="0"
         y="420.28513"
         x="152.77583" /></tspan><tspan
       id="tspan4817"
       y="430.09326"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4819"
         y="430.09326"
         x="39.688015">+    info.SetColumn(col);</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4821"
         dx="0"
         y="430.09326"
         x="162.60954" /></tspan><tspan
       id="tspan4823"
       y="439.90146"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4825"
         y="439.90146"
         x="39.688015">+    info.SetMask(wxLIST_MASK_TEXT);</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4827"
         dx="0"
         y="439.90146"
         x="216.69504" /></tspan><tspan
       id="tspan4829"
       y="449.70963"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4831"
         y="449.70963"
         x="39.688015">+    lst-&gt;GetItem(info);</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4833"
         dx="0"
         y="449.70963"
         x="157.6927" /></tspan><tspan
       id="tspan4835"
       y="459.51776"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4837"
         y="459.51776"
         x="39.688015">+    return info.GetText();</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4839"
         dx="0"
         y="459.51776"
         x="172.44328" /></tspan><tspan
       id="tspan4841"
       y="469.32596"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4843"
         y="469.32596"
         x="39.688015">+}</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4845"
         dx="0"
         y="469.32596"
         x="49.521736" /></tspan><tspan
       id="tspan4847"
       y="479.13412"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4849"
         y="479.13412"
         x="39.688015">+</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4851"
         dx="0"
         y="479.13412"
         x="44.604874" /></tspan><tspan
       id="tspan4853"
       y="488.94229"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4855"
         y="488.94229"
         x="39.688015"> void UpdateDlg::SetListColumnText(int idx, int col, const wxString&amp; text)</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4857"
         dx="0"
         y="488.94229"
         x="403.53577" /></tspan><tspan
       id="tspan4859"
       y="498.75046"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4861"
         y="498.75046"
         x="39.688015"> {</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4863"
         dx="0"
         y="498.75046"
         x="49.521736" /></tspan><tspan
       id="tspan4865"
       y="508.55865"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4867"
         y="508.55865"
         x="39.688015">     wxListCtrl* lst = XRCCTRL(*this, &quot;lvFiles&quot;, wxListCtrl);</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4869"
         dx="0"
         y="508.55865"
         x="339.61658" /></tspan><tspan
       id="tspan4871"
       y="518.36682"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4873"
         y="518.36682"
         x="39.688015">@@ -393,7 +407,9 @@</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4875"
         dx="0"
         y="518.36682"
         x="133.10837" /></tspan><tspan
       id="tspan4877"
       y="528.17499"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4879"
         y="528.17499"
         x="39.688015">     if (index == -1)</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4881"
         dx="0"
         y="528.17499"
         x="142.94209" /></tspan><tspan
       id="tspan4883"
       y="537.98315"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4885"
         y="537.98315"
         x="39.688015">         return 0;</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4887"
         dx="0"
         y="537.98315"
         x="128.19151" /></tspan><tspan
       id="tspan4889"
       y="547.79132"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4891"
         y="547.79132"
         x="39.688015">     wxString title = lst-&gt;GetItemText(index);</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4893"
         dx="0"
         y="547.79132"
         x="265.86365" /></tspan><tspan
       id="tspan4895"
       y="557.59955"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4897"
         y="557.59955"
         x="39.688015">-    return FindRecByTitle(title, m_Recs, m_RecsCount);</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4899"
         dx="0"
         y="557.59955"
         x="310.11542" /></tspan><tspan
       id="tspan4901"
       y="567.40765"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4903"
         y="567.40765"
         x="39.688015">+    wxString version = GetListColumnText(index, 1);</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4905"
         dx="0"
         y="567.40765"
         x="295.36481" /></tspan><tspan
       id="tspan4907"
       y="577.21588"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4909"
         y="577.21588"
         x="39.688015">+    wxString revision = GetListColumnText(index, 4);</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4911"
         dx="0"
         y="577.21588"
         x="300.28168" /></tspan><tspan
       id="tspan4913"
       y="587.02405"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4915"
         y="587.02405"
         x="39.688015">+    return FindRec(title, version, revision, m_Recs, m_RecsCount);</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4917"
         dx="0"
         y="587.02405"
         x="369.11774" /></tspan><tspan
       id="tspan4919"
       y="596.83228"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4921"
         y="596.83228"
         x="39.688015"> }</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4923"
         dx="0"
         y="596.83228"
         x="49.521736" /></tspan><tspan
       id="tspan4925"
       y="606.6405"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4927"
         y="606.6405"
         x="39.688015"> </tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4929"
         dx="0"
         y="606.6405"
         x="44.604874" /></tspan><tspan
       id="tspan4931"
       y="616.44867"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4933"
         y="616.44867"
         x="39.688015"> void UpdateDlg::DownloadFile(bool dontInstall)</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4935"
         dx="0"
         y="616.44867"
         x="270.78052" /></tspan><tspan
       id="tspan4937"
       y="626.25684"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4939"
         y="626.25684"
         x="39.688015">Index: src/plugins/contrib/devpak_plugin/manifest.xml</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4941"
         dx="0"
         y="626.25684"
         x="300.28168" /></tspan><tspan
       id="tspan4943"
       y="636.06506"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4945"
         y="636.06506"
         x="39.688015">===================================================================</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4947"
         dx="0"
         y="636.06506"
         x="369.11774" /></tspan><tspan
       id="tspan4949"
       y="645.87329"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4951"
         y="645.87329"
         x="39.688015">--- src/plugins/contrib/devpak_plugin/manifest.xml (revision 5106)</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4953"
         dx="0"
         y="645.87329"
         x="364.20087" /></tspan><tspan
       id="tspan4955"
       y="655.68146"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4957"
         y="655.68146"
         x="39.688015">+++ src/plugins/contrib/devpak_plugin/manifest.xml (working copy)</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4959"
         dx="0"
         y="655.68146"
         x="359.28403" /></tspan><tspan
       id="tspan4961"
       y="665.48969"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4963"
         y="665.48969"
         x="39.688015">@@ -2,18 +2,19 @@</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4965"
         dx="0"
         y="665.48969"
         x="123.27467" /></tspan><tspan
       id="tspan4967"
       y="675.29791"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4969"
         y="675.29791"
         x="39.688015"> &lt;CodeBlocks_plugin_manifest_file&gt;</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4971"
         dx="0"
         y="675.29791"
         x="206.86131" /></tspan><tspan
       id="tspan4973"
       y="685.10602"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4975"
         y="685.10602"
         x="39.688015">     &lt;SdkVersion major=&quot;1&quot; minor=&quot;10&quot; release=&quot;0&quot; /&gt;</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4977"
         dx="0"
         y="685.10602"
         x="295.36481" /></tspan><tspan
       id="tspan4979"
       y="694.91425"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4981"
         y="694.91425"
         x="39.688015">     &lt;Plugin name=&quot;DevPakUpdater&quot;&gt;</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4983"
         dx="0"
         y="694.91425"
         x="206.86131" /></tspan><tspan
       id="tspan4985"
       y="704.72247"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4987"
         y="704.72247"
         x="39.688015">-        &lt;Value title=&quot;Dev-C++ DevPak updater/installer&quot; /&gt;</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4989"
         dx="0"
         y="704.72247"
         x="329.78284" /></tspan><tspan
       id="tspan4991"
       y="714.5307"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4993"
         y="714.5307"
         x="39.688015">-        &lt;Value version=&quot;0.1&quot; /&gt;</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4995"
         dx="0"
         y="714.5307"
         x="197.02759" /></tspan><tspan
       id="tspan4997"
       y="724.33887"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan4999"
         y="724.33887"
         x="39.688015">+        &lt;Value title=&quot;DevPak updater/installer&quot; /&gt;</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5001"
         dx="0"
         y="724.33887"
         x="290.44797" /></tspan><tspan
       id="tspan5003"
       y="734.14709"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5005"
         y="734.14709"
         x="39.688015">+        &lt;Value version=&quot;0.2&quot; /&gt;</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5007"
         dx="0"
         y="734.14709"
         x="197.02759" /></tspan><tspan
       id="tspan5009"
       y="743.95526"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5011"
         y="743.95526"
         x="39.688015">         &lt;Value description=&quot;Installs selected DevPaks from the Internet&quot; /&gt;</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5013"
         dx="0"
         y="743.95526"
         x="413.36951" /></tspan><tspan
       id="tspan5015"
       y="753.76343"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5017"
         y="753.76343"
         x="39.688015">         &lt;Value author=&quot;Yiannis Mandravellos&quot; /&gt;</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5019"
         dx="0"
         y="753.76343"
         x="275.69739" /></tspan><tspan
       id="tspan5021"
       y="763.57166"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5023"
         y="763.57166"
         x="39.688015">         &lt;Value authorEmail=&quot;<EMAIL>&quot; /&gt;</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5025"
         dx="0"
         y="763.57166"
         x="295.36481" /></tspan><tspan
       id="tspan5027"
       y="773.37988"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5029"
         y="773.37988"
         x="39.688015">         &lt;Value authorWebsite=&quot;http://www.codeblocks.org/&quot; /&gt;</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5031"
         dx="0"
         y="773.37988"
         x="339.61658" /></tspan><tspan
       id="tspan5033"
       y="783.18805"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5035"
         y="783.18805"
         x="39.688015">         &lt;Value thanksTo=&quot;Dev-C++ community.</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5037"
         dx="0"
         y="783.18805"
         x="256.02994" /></tspan><tspan
       id="tspan5039"
       y="792.99628"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5041"
         y="792.99628"
         x="39.688015">-                         Julian R Seward for libbzip2.</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5043"
         dx="0"
         y="792.99628"
         x="310.11542" /></tspan><tspan
       id="tspan5045"
       y="802.8045"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5047"
         y="802.8045"
         x="39.688015">-                         libbzip2 copyright notice:</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5049"
         dx="0"
         y="802.8045"
         x="295.36481" /></tspan><tspan
       id="tspan5051"
       y="812.61267"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5053"
         y="812.61267"
         x="39.688015">-                         bzip2 and associated library libbzip2, are</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5055"
         dx="0"
         y="812.61267"
         x="374.03461" /></tspan><tspan
       id="tspan5057"
       y="822.42084"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5059"
         y="822.42084"
         x="39.688015">-                         copyright (C) 1996-2000 Julian R Seward.</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5061"
         dx="0"
         y="822.42084"
         x="364.20087" /></tspan><tspan
       id="tspan5063"
       y="832.22906"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5065"
         y="832.22906"
         x="39.688015">-                         All rights reserved.&quot; /&gt;</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5067"
         dx="0"
         y="832.22906"
         x="285.5311" /></tspan><tspan
       id="tspan5069"
       y="842.03729"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5071"
         y="842.03729"
         x="39.688015">+        Julian R Seward for libbzip2.</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5073"
         dx="0"
         y="842.03729"
         x="226.52878" /></tspan><tspan
       id="tspan5075"
       y="851.84546"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5077"
         y="851.84546"
         x="39.688015">+</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5079"
         dx="0"
         y="851.84546"
         x="44.604874" /></tspan><tspan
       id="tspan5081"
       y="861.65369"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5083"
         y="861.65369"
         x="39.688015">+        libbzip2 copyright notice:</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5085"
         dx="0"
         y="861.65369"
         x="211.77817" /></tspan><tspan
       id="tspan5087"
       y="871.46185"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5089"
         y="871.46185"
         x="39.688015">+        bzip2 and associated library libbzip2, are</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5091"
         dx="0"
         y="871.46185"
         x="290.44797" /></tspan><tspan
       id="tspan5093"
       y="881.27008"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5095"
         y="881.27008"
         x="39.688015">+        copyright (C) 1996-2000 Julian R Seward.</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5097"
         dx="0"
         y="881.27008"
         x="280.61423" /></tspan><tspan
       id="tspan5099"
       y="891.07825"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5101"
         y="891.07825"
         x="39.688015">+        All rights reserved.&quot; /&gt;</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5103"
         dx="0"
         y="891.07825"
         x="201.94443" /></tspan><tspan
       id="tspan5105"
       y="900.88647"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5107"
         y="900.88647"
         x="39.688015">         &lt;Value license=&quot;GPL&quot; /&gt;</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5109"
         dx="0"
         y="900.88647"
         x="197.02759" /></tspan><tspan
       id="tspan5111"
       y="910.6947"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5113"
         y="910.6947"
         x="39.688015">     &lt;/Plugin&gt;</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5115"
         dx="0"
         y="910.6947"
         x="108.52407" /></tspan><tspan
       id="tspan5117"
       y="920.50287"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5119"
         y="920.50287"
         x="39.688015">-&lt;/CodeBlocks_plugin_manifest_file&gt;</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5121"
         dx="0"
         y="920.50287"
         x="211.77817" /></tspan><tspan
       id="tspan5123"
       y="930.31104"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5125"
         y="930.31104"
         x="39.688015">+&lt;/CodeBlocks_plugin_manifest_file&gt;</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5127"
         dx="0"
         y="930.31104"
         x="211.77817" /></tspan><tspan
       id="tspan5129"
       y="940.11926"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5131"
         y="940.11926"
         x="39.688015">\ No newline at end of file</tspan><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5133"
         dx="0"
         y="940.11926"
         x="172.44328" /></tspan><tspan
       id="tspan5135"
       y="940.11926"
       x="39.688015"><tspan
         style="font-size:8.17348385px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:120.00000477%;writing-mode:lr-tb;text-anchor:start;font-family:Lucida Console;-inkscape-font-specification:Lucida Console"
         id="tspan5137"
         y="949.92749"
         x="39.688015" /></tspan></text>
  <text
     sodipodi:linespacing="125%"
     style="font-size:8.20018482px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:center;line-height:125%;writing-mode:lr-tb;text-anchor:middle;fill:#000000;fill-opacity:1;stroke:none;stroke-width:1px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1;font-family:DejaVu Sans Mono;-inkscape-font-specification:DejaVu Sans Mono"
     xml:space="preserve"
     id="text3015"
     y="250.75372"
     x="640.2099"
     transform="scale(1.0216856,0.9787747)" />
  <text
     sodipodi:linespacing="125%"
     style="font-size:8.20018482px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:center;line-height:125%;writing-mode:lr-tb;text-anchor:middle;fill:#000000;fill-opacity:1;stroke:none;stroke-width:1px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1;font-family:DejaVu Sans Mono;-inkscape-font-specification:DejaVu Sans Mono"
     xml:space="preserve"
     y="250.75372"
     x="640.2099"
     transform="scale(1.0216856,0.9787747)"
     id="text3035" />
  <text
     sodipodi:linespacing="125%"
     style="font-size:8.20018482px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:center;line-height:125%;writing-mode:lr-tb;text-anchor:middle;fill:#000000;fill-opacity:1;stroke:none;stroke-width:1px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1;font-family:DejaVu Sans Mono;-inkscape-font-specification:DejaVu Sans Mono"
     xml:space="preserve"
     id="text3067"
     y="248.54395"
     x="640.2099"
     transform="scale(1.0216856,0.9787747)"><tspan
       style="font-size:8.20018482px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:center;line-height:125%;writing-mode:lr-tb;text-anchor:middle;fill:#000000;fill-opacity:1;font-family:DejaVu Sans Mono;-inkscape-font-specification:DejaVu Sans Mono"
       id="tspan3069"
       y="248.54395"
       x="640.2099" /></text>
  <g
     transform="matrix(1.0472513,0,0,1.0032667,147.41284,155.66249)"
     id="g3096">
    <g
       id="g3106"
       transform="translate(0,5.9804637)">
      <rect
         style="fill:#ffcfc9;fill-opacity:1;stroke:#000000;stroke-width:0.97558779;stroke-opacity:1"
         id="rect3098"
         y="70.67984"
         x="423.36264"
         height="27.274118"
         width="122.22845" />
      <text
         sodipodi:linespacing="125%"
         style="font-size:8px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:center;line-height:125%;writing-mode:lr-tb;text-anchor:middle;fill:#000000;fill-opacity:1;stroke:none;stroke-width:1px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1;font-family:DejaVu Sans Mono;-inkscape-font-specification:DejaVu Sans Mono"
         xml:space="preserve"
         id="text3100"
         y="87.320808"
         x="483.81927">line numbers start from 1</text>
    </g>
  </g>
</svg>
