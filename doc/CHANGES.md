##### 1.16

  - Python 3 support, thanks to <PERSON><PERSON> (@yan12125)
    (pull request #36)

##### 1.15

  - Project moved to GitHub
  - patch-1.15.zip archive is now executable
  - improved Git patch detection thanks to @mspncp (#32)
  - tests/data contains database of all possible patches
  - tests suite scan now processes tests/data automatically
  - API changes:
    + setdebug() initializes logging and enables debug info

##### 1.14.2

   - --revert option to apply patches in reverse order (unpatch)
   - support for broken patches generated by online Google Code editor
   - API changes:
     + PatchSet and Patch objects are now iterable
     + new PatchSet.findfile() contains logic detecting filename to patch
     + PatchSet.revert()
   - make directory based tests easier to create and run manually
   - fix xnormpath with Windows paths on Linux
     (issue #24, found by <PERSON>)

##### 1.13

   - diffstat output now also shows size delta in bytes
   - added --directory (-d) option to specify root when applying patches
   - hunk headers produced by `diff -p` option are now parsed and accessible
     (issue #22, found by <PERSON>)
   - API changes:
     + Hunk.desc field to access hunk headers content
     + PatchSet.apply() gets `root` keyword argument for the working dir
       when applying patches (issue #7)
   - improve error message for missing files
   - improve docs (fix issue #5)

##### 1.12.11  Major API Break

   - patch.py can read patches from stdin
   - patch.py can show nice histogram with --diffstat option
   - added detection of SVN, GIT and HG patch types, unrecognized
     patches marked as PLAIN
   - added error reporting for parsing functions and helpers (now they
     return False if parsing failed) - make sure you handle this correctly
   - added normalization to filenames to protect against patching files
     using absolute paths or files in parent directories
   - test run patch.py on all patches submitted to Python bug tracker, which
     resulted in improved parsing and error handling for some corner cases
   - improved logging
   - API changes
     * fromfile(), fromstring() and fromurl() now return False on errors
     * previous Patch is renamed to PatchSet, new Patch is single file entry
     * Patch.header is now a list of strings
     * PatchSet.parse() now returns True if parsing completed without errors
     + PatchSet.__len__()
     + PatchSet.diffstat()
     + PatchSet.type and Patch.type
     + PatchSet.errors and 
     + xisabs() cross-platform version of `os.path.isabs()`
     + xnormpath() forward slashed version of `os.path.normpath()`
     + xstrip() to strip absolute path prefixes

##### 11.01

   - patch.py can read patches from web
   - patch.py returns -1 if there were errors during patching
   - store patch headers (necessary for future DIFF/SVN/HG/GIT detection)
   - report about extra bytes at the end after patch is parsed
   - API changes
     + fromurl()
     * Patch.apply() now returns True on success

##### 10.11

   - fixed fromstring() failure due to invalid StringIO import (issue #9)
     (thanks john.stumpo for reporting)
   - added --verbose and --quiet options
   - improved message logging
   - change "successfully patched..." message to INFO instead of WARN
     (thanks Alex Stewart for reporting and patch)
   - skip __main__ imports when used as a library (patch by Alex Stewart)
   - API changes
      * renamed class HunkInfo to Hunk
      + Patch.type placeholder (no detection yet - parser is not ready)
      + constants for patch types DIFF/PLAIN, HG/MERCURIAL, SVN/SUBVERSION
      + Patch.header for saving headers which can be used later to extract
        additional meta information such as commit message
   - internal: improving parser speed by allowing blocks fetch lines on
               demand
   - test suite improvements

##### 10.04

   - renamed debug option to --debug
   - API changes
     * method names are now underscored for consistency with difflib
     + addded Patch.can_patch(filename) to test if source file is in list
       of source filenames and can be patched
     * use designated logger "python_patch" instead of default

##### 9.08-2

   - compatibility fix for Python 2.4

##### 9.08-1

   - fixed issue #2 - remove trailing whitespaces from filename
     (thanks James from Twisted Fish)
   - API changes
     + added Patch and HunkInfo classes
     * moved utility methods into Patch
     + build Patch object by specifying stream to constructor
       or use top level functions fromfile() and fromstring()
   - added test suite

##### 8.06-2

   - compatibility fix for Python 2.4

##### 8.06-1

   - initial release
